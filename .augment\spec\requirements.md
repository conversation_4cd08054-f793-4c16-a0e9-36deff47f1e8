# Deepseek模型加密货币永续合约全自动量化系统 需求文档

## 功能概述
基于deepseek AI模型的加密货币永续合约全自动量化交易系统，通过欧易交易所进行保证金交易，采用全仓模式和单向持仓策略。系统集成市场数据分析、AI决策引擎、风险管理和人性化前端界面，实现完全自动化的量化交易。

## 需求列表

### 1. 系统架构与技术栈
**用户故事：** 作为系统管理员，我希望系统采用现代化的技术栈，以便确保系统的稳定性、可维护性和扩展性。

**验收标准：**
1. 当系统启动时，后端应该基于Python + FastAPI框架运行
2. 当用户访问前端时，系统应该使用Jinja2模板引擎 + Bootstrap 5.3.7提供响应式界面
3. 当系统需要市场数据时，应该完全依托ccxt库获取欧易交易所数据
4. 当系统进行技术分析时，应该使用ta-lib库进行指标计算
5. 当系统需要存储配置时，应该使用sqlite3数据库而非配置文件

### 2. 交易所集成与数据获取
**用户故事：** 作为交易员，我希望系统能够获取准确的市场数据，以便AI模型做出正确的交易决策。

**验收标准：**
1. 当系统运行时，应该支持欧易交易所的模拟盘和实盘切换
2. 当获取市场数据时，系统应该采用RESTful API轮询方式，不使用WebSocket
3. 当分析市场时，系统应该获取1分钟、5分钟、15分钟、1小时四个时间周期的K线数据
4. 当获取数据时，系统应该包含价格、成交量等完整市场信息
5. 当系统启动时，应该能够获取账户可用资金而非总资产

### 3. AI决策引擎设计
**用户故事：** 作为量化交易者，我希望系统具备智能的开仓和持仓决策能力，以便最大化交易收益并控制风险。

**验收标准：**
1. 当系统分析市场时，应该将ta-lib计算结果发送给deepseek AI模型
2. 当AI分析完成时，系统应该返回置信度（0-100）和决策理由
3. 当需要开仓时，系统应该使用专门的AI开仓引擎和对应提示词
4. 当管理持仓时，系统应该使用独立的AI持仓引擎和不同提示词
5. 当AI返回结果时，系统应该根据置信度和用户设置的参数执行交易决策

### 4. 交易执行与风险管理
**用户故事：** 作为风险管理者，我希望系统能够严格控制交易风险，以便保护投资资金安全。

**验收标准：**
1. 当执行交易时，系统应该采用保证金交易模式和全仓模式
2. 当开仓时，系统应该仅支持单向持仓（多头或空头）
3. 当设置止盈止损时，系统应该考虑杠杆放大效应
4. 当计算止盈止损时，系统应该根据买入方向（多头/空头）进行相应调整
5. 当执行交易时，系统应该根据最大杠杆、最大仓位等全局参数进行风险控制

### 5. 参数配置与数据存储
**用户故事：** 作为系统配置员，我希望能够灵活配置交易参数，以便适应不同的交易策略和风险偏好。

**验收标准：**
1. 当存储敏感信息时，系统应该将API密钥存储在sqlite3数据库中
2. 当配置交易参数时，系统应该支持全局设置而非单个交易对设置
3. 当用户设置参数时，系统应该包含最大杠杆、最大仓位、止盈止损等配置项
4. 当系统运行时，应该不存储市场数据、账户数据、交易记录等，完全依赖ccxt接口获取
5. 当需要历史数据时，系统应该实时从交易所获取而非本地缓存

### 6. 前端用户界面
**用户故事：** 作为交易员，我希望拥有直观易用的前端界面，以便监控交易状态和调整系统参数。

**验收标准：**
1. 当用户访问系统时，界面应该符合人性化设计原则
2. 当用户操作时，界面应该基于Bootstrap 5.3.7提供响应式体验
3. 当选择交易对时，用户应该能够从前端选择需要交易的加密货币对
4. 当监控交易时，界面应该显示当前持仓、收益、风险等关键信息
5. 当调整参数时，用户应该能够实时修改交易配置并立即生效

### 7. 系统监控与日志
**用户故事：** 作为系统运维人员，我希望能够监控系统运行状态，以便及时发现和解决问题。

**验收标准：**
1. 当系统运行时，应该记录关键操作和决策过程的日志
2. 当发生错误时，系统应该提供详细的错误信息和堆栈跟踪
3. 当AI做出决策时，系统应该记录置信度、理由和最终执行结果
4. 当交易执行时，系统应该记录交易类型、数量、价格等关键信息
5. 当系统异常时，应该能够通过日志快速定位问题原因

### 8. 安全性与稳定性
**用户故事：** 作为系统安全管理员，我希望系统具备高度的安全性和稳定性，以便保护用户资产和数据安全。

**验收标准：**
1. 当存储API密钥时，系统应该采用加密存储方式
2. 当网络请求失败时，系统应该具备重试机制和错误处理
3. 当AI服务不可用时，系统应该有降级策略避免盲目交易
4. 当检测到异常波动时，系统应该能够紧急停止交易
5. 当系统重启时，应该能够恢复到正常运行状态而不丢失关键配置
